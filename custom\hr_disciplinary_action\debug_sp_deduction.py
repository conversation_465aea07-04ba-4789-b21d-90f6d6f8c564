#!/usr/bin/env python3
"""
Debug script for SP Deduction issues
Run this script in Odoo shell to diagnose SP deduction problems

Usage:
python odoo-bin shell -d your_database
>>> exec(open('custom/hr_disciplinary_action/debug_sp_deduction.py').read())
"""

def debug_sp_deduction():
    """Debug SP deduction system"""
    print("=== SP Deduction Debug Report ===\n")
    
    # 1. Check if salary rule exists
    print("1. Checking Salary Rule...")
    salary_rule = env['hr.salary.rule'].search([('code', '=', 'SP_DED')])
    if salary_rule:
        print(f"✅ SP Deduction salary rule found: {salary_rule.name}")
        print(f"   - Active: {salary_rule.active}")
        print(f"   - Condition: {salary_rule.condition_python}")
    else:
        print("❌ SP Deduction salary rule NOT found!")
        print("   Solution: Update module or check data files")
    print()
    
    # 2. Check employees with different employment states
    print("2. Checking Employees by Employment State...")
    employment_states = ['permanent', 'contract', 'probation', 'part_time']
    for state in employment_states:
        contracts = env['hr.contract'].search([
            ('employment_state', '=', state),
            ('state', '=', 'open')
        ])
        print(f"   - {state.title()}: {len(contracts)} active contracts")
    print()
    
    # 3. Check active disciplinary actions
    print("3. Checking Active Disciplinary Actions...")
    active_sps = env['hr.disciplinary.action'].search([
        ('state', '=', 'approved'),
        ('additional_salary_deduction', '=', True),
        ('remaining_validity', '>', 0)
    ])
    print(f"   Total active SPs with deduction: {len(active_sps)}")
    
    for sp in active_sps:
        contract = env['hr.contract'].search([
            ('employee_id', '=', sp.employee_id.id),
            ('state', '=', 'open')
        ], limit=1)
        employment_state = contract.employment_state if contract else 'No Contract'
        print(f"   - {sp.employee_id.name} ({employment_state}): {sp.salary_deduction_percentage}% for {sp.remaining_validity} months")
    print()
    
    # 4. Check recent payslips with SP detection
    print("4. Checking Recent Payslips with SP Detection...")
    recent_payslips = env['hr.payslip'].search([
        ('has_sp_deduction', '=', True)
    ], limit=10, order='create_date desc')
    
    print(f"   Recent payslips with SP: {len(recent_payslips)}")
    for payslip in recent_payslips:
        contract = payslip.contract_id
        employment_state = contract.employment_state if contract else 'Unknown'
        print(f"   - {payslip.employee_id.name} ({employment_state}): {payslip.sp_deduction_percentage}%")
        
        # Check if SP Deduction line exists in payslip
        sp_line = payslip.line_ids.filtered(lambda l: l.code == 'SP_DED')
        if sp_line:
            print(f"     ✅ SP Deduction line: {sp_line.total}")
        else:
            print(f"     ❌ SP Deduction line missing!")
    print()
    
    # 5. Test SP detection for specific employee types
    print("5. Testing SP Detection by Employment Type...")
    
    # Find part-time employees with active contracts
    part_time_contracts = env['hr.contract'].search([
        ('employment_state', '=', 'part_time'),
        ('state', '=', 'open')
    ], limit=3)
    
    if part_time_contracts:
        print("   Part-time employees:")
        for contract in part_time_contracts:
            employee = contract.employee_id
            # Check if employee has any SPs
            sps = env['hr.disciplinary.action'].search([
                ('employee_id', '=', employee.id),
                ('state', '=', 'approved'),
                ('additional_salary_deduction', '=', True)
            ])
            print(f"   - {employee.name}: {len(sps)} SP(s)")
            
            # Check recent payslips
            payslips = env['hr.payslip'].search([
                ('employee_id', '=', employee.id)
            ], limit=1, order='create_date desc')
            
            if payslips:
                payslip = payslips[0]
                print(f"     Last payslip SP detection: {payslip.has_sp_deduction}")
    else:
        print("   No part-time employees found")
    print()
    
    # 6. Recommendations
    print("6. Recommendations...")
    if not salary_rule:
        print("   ❌ Install/update module to create salary rule")
    if not active_sps:
        print("   ⚠️  No active SPs found - create test SP to verify")
    if not recent_payslips:
        print("   ⚠️  No recent payslips with SP - create test payslip")
    
    print("\n=== Debug Report Complete ===")

def create_test_sp_for_part_time():
    """Create a test SP for a part-time employee"""
    print("Creating test SP for part-time employee...")
    
    # Find or create part-time employee
    part_time_contract = env['hr.contract'].search([
        ('employment_state', '=', 'part_time'),
        ('state', '=', 'open')
    ], limit=1)
    
    if not part_time_contract:
        print("No part-time employee found. Creating test employee...")
        
        # Create department
        department = env['hr.department'].create({
            'name': 'Test Department',
            'schedule_prorate': 23
        })
        
        # Create employee
        employee = env['hr.employee'].create({
            'name': 'Test Part Time Employee',
            'department_id': department.id
        })
        
        # Create contract
        part_time_contract = env['hr.contract'].create({
            'name': 'Test Part Time Contract',
            'employee_id': employee.id,
            'wage': 3000000,
            'employment_state': 'part_time',
            'state': 'open',
            'date_start': '2024-01-01'
        })
    
    employee = part_time_contract.employee_id
    
    # Find or create disciplinary type
    disc_type = env['hr.disciplinary.type'].search([], limit=1)
    if not disc_type:
        disc_type = env['hr.disciplinary.type'].create({
            'name': 'Test Violation',
            'description': 'Test disciplinary type'
        })
    
    # Create SP
    sp = env['hr.disciplinary.action'].create({
        'employee_id': employee.id,
        'type_id': disc_type.id,
        'action': 'sp1',
        'additional_salary_deduction': True,
        'salary_deduction_percentage': 10.0,
        'salary_deduction_validity': 2
    })
    
    # Submit and approve
    sp.action_submit()
    sp.action_approve()
    
    print(f"✅ Created and approved SP for {employee.name}")
    print(f"   SP ID: {sp.id}")
    print(f"   Deduction: {sp.salary_deduction_percentage}%")
    print(f"   Validity: {sp.remaining_validity} months")
    
    return sp

# Run debug
debug_sp_deduction()

# Uncomment to create test SP
# create_test_sp_for_part_time()
