# SP Deduction Troubleshooting Guide

## 🚨 Error: XMLSyntaxError - StartTag: invalid element name

**Problem**: XML syntax error saat upgrade module
**Cause**: <PERSON><PERSON><PERSON> `<` dan `>` di dalam Python code dianggap sebagai XML tag

**Solution**:
1. ✅ **Fixed**: Menggunakan CDATA wrapper untuk Python code
2. ✅ **Alternative**: Menggunakan file `sp_deduction_salary_rule_simple.xml`

## 🚨 Error: kolom hr_payslip.sp_deduction_amount belum ada

**Problem**: Field `sp_deduction_amount` belum ter-create di database
**Cause**: Computed field dengan store=True memerlukan migration

**Solution**:
```sql
-- Manual database fix
ALTER TABLE hr_payslip ADD COLUMN IF NOT EXISTS sp_deduction_amount NUMERIC DEFAULT 0.0;
UPDATE hr_payslip SET sp_deduction_amount = 0.0 WHERE sp_deduction_amount IS NULL;
```

## 🔧 Step-by-Step Fix

### 1. Stop Odoo Service
```bash
sudo systemctl stop odoo
```

### 2. Manual Database Update
```sql
psql -U odoo -d your_database_name

-- Add column
ALTER TABLE hr_payslip ADD COLUMN IF NOT EXISTS sp_deduction_amount NUMERIC DEFAULT 0.0;

-- Update existing records
UPDATE hr_payslip SET sp_deduction_amount = 0.0 WHERE sp_deduction_amount IS NULL;

-- Exit
\q
```

### 3. Update Module
```bash
# Command line update
python odoo-bin -u hr_disciplinary_action -d your_database_name --stop-after-init

# Or restart and upgrade from Apps menu
sudo systemctl start odoo
```

### 4. Verify Installation

#### Check Salary Rule:
1. Go to **Payroll > Configuration > Salary Rules**
2. Search for "SP Deduction" or code "SP_DED"
3. Verify condition: `result = payslip.has_sp_deduction`

#### Check Salary Structure:
1. Go to **Payroll > Configuration > Salary Structures**
2. Open your salary structure
3. Add "SP Deduction" to Salary Rules if not present

## 🧪 Testing

### 1. Create Test Disciplinary Action
```
Payroll > Performance > Disciplinary Actions > Create

Fields:
- Employee: [Select employee]
- Type: [Any type]
- Action: SP I
- Additional Salary Deduction: ✓
- Salary Deduction Percentage: 10
- Submit > Approve
```

### 2. Generate Test Payslip
```
Payroll > Payslips > Create
- Employee: [Same employee with SP]
- Compute Sheet
- Check for "SP Deduction" line
```

### 3. Expected Results
- ✅ **Has SP** indicator in payslip tree view
- ✅ **SP Deduction Information** group in payslip form
- ✅ **SP Deduction** line in payslip lines with negative amount

## 🔍 Common Issues

### Issue 1: SP Deduction tidak muncul
**Check**:
- Employee memiliki Disciplinary Action approved ✓
- Additional Salary Deduction = True ✓
- Remaining validity > 0 ✓
- Salary rule SP_DED ada di structure ✓

### Issue 2: Perhitungan salah
**Check**:
- Kategori salary rules (Basic, Allowance, Deduction) ✓
- Employment state employee ✓
- Working days di payslip ✓
- Schedule prorate di department ✓

### Issue 3: Error saat compute
**Check**:
- Log error di Settings > Technical > Logging
- Field has_sp_deduction dan sp_deduction_percentage computed
- Database schema up to date

## 📋 Manual Salary Rule Setup

Jika automatic installation gagal, buat manual:

### 1. Create Salary Rule
```
Name: SP Deduction
Code: SP_DED
Sequence: 200
Category: Deduction
Condition Based on: Python Expression
Python Condition: result = payslip.has_sp_deduction
Amount Type: Python Code
```

### 2. Python Code (Simple Version)
```python
if not payslip.has_sp_deduction:
    result = 0.0
else:
    # Calculate total earnings
    total_earnings = 0.0
    for line in payslip.line_ids:
        if line.category_id.name in ['Basic', 'Allowance']:
            total_earnings += line.total

    # Calculate total deductions (excluding SP)
    total_deductions = 0.0
    for line in payslip.line_ids:
        if line.category_id.name == 'Deduction' and line.code != 'SP_DED':
            total_deductions += line.total

    # Base amount
    base_amount = total_earnings + total_deductions

    # Handle employment types
    if contract.employment_state == 'part_time':
        working_days = 0
        for slip in payslip.worked_days_line_ids:
            if slip.sequence == 1:
                working_days = slip.number_of_days or 0
        if working_days > 0:
            base_amount = base_amount * working_days / 30
    else:
        # Check prorate (simplified)
        prorate = False
        join_date = contract.employee_id.join_date
        resign_date = contract.employee_id.resign_date or contract.date_end
        
        if join_date and payslip.date_from <= join_date <= payslip.date_to:
            prorate = True
        if resign_date and payslip.date_from <= resign_date <= payslip.date_to:
            prorate = True

        if prorate:
            schedule_prorate = contract.employee_id.department_id.schedule_prorate or 23
            working_days = 0
            for slip in payslip.worked_days_line_ids:
                if slip.sequence == 1:
                    working_days = slip.number_of_days or 0

            if working_days > 0:
                base_amount = base_amount * working_days / schedule_prorate

    # Calculate SP deduction
    result = -1 * (base_amount * payslip.sp_deduction_percentage / 100)
```

## 🎯 Final Checklist

- ✅ Module upgraded successfully
- ✅ Database schema updated
- ✅ Salary rule created/exists
- ✅ Salary rule added to structure
- ✅ Test disciplinary action created
- ✅ Test payslip generated
- ✅ SP deduction line appears
- ✅ Calculation correct

## 📞 Support

Jika masih ada masalah:
1. Check Odoo log: `/var/log/odoo/odoo.log`
2. Check database connection
3. Verify module dependencies
4. Restart Odoo service completely
