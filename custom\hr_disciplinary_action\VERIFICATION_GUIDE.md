# SP Deduction Verification Guide

## Overview
This guide helps you verify that the SP (Surat Peringatan) deduction system works correctly for all employee types: part-time, prorate, and full-time employees.

## Prerequisites
1. Module `hr_disciplinary_action` must be installed and updated
2. Salary rule "SP Deduction" (code: SP_DED) must be active
3. Employees must have active contracts with correct employment_state

## Step-by-Step Verification

### 1. Check Salary Rule Installation
```sql
-- Check if SP Deduction salary rule exists
SELECT name, code, active, condition_python 
FROM hr_salary_rule 
WHERE code = 'SP_DED';
```

Expected result: One record with name "SP Deduction", code "SP_DED", active=True

### 2. Test Part-Time Employee SP Creation

#### Create Part-Time Employee:
1. Go to Employees > Employees
2. Create new employee with:
   - Name: "Test Part Time Employee"
   - Department: Any department with schedule_prorate > 0

#### Create Part-Time Contract:
1. Go to Employees > Contracts
2. Create contract with:
   - Employee: Test Part Time Employee
   - Employment Status: Part-Time
   - Wage: 3,000,000
   - State: Running

#### Create SP for Part-Time Employee:
1. Go to Employees > Disciplinary Actions
2. Create new disciplinary action:
   - Employee: Test Part Time Employee
   - Type: Any type
   - Action: SP I
   - Additional Salary Deduction: ✓
   - Salary Deduction Percentage: 10%
   - Salary Deduction Validity: 2 months
3. Submit and Approve the SP

**Expected Result**: SP should be created and approved successfully

### 3. Test Payslip SP Detection for Part-Time

#### Create Payslip:
1. Go to Payroll > Payslips
2. Create payslip for Test Part Time Employee
3. Set period: Current month
4. Compute Sheet

#### Verify SP Detection:
1. Check payslip fields:
   - `has_sp_deduction` should be True
   - `sp_deduction_percentage` should be 10.0
   - `disciplinary_action_ids` should contain the SP record

#### Verify SP Deduction Rule:
1. Look for "SP Deduction" line in payslip
2. Amount should be negative (deduction)
3. Calculation should be proportional to working days for part-time

**Expected Result**: SP Deduction rule appears and calculates correctly

### 4. Test Full-Time Employee SP

#### Create Full-Time Employee and Contract:
- Employment Status: Permanent or Contract (not Part-Time)
- Follow same steps as part-time test

**Expected Result**: SP creation and payslip detection work the same

### 5. Test Prorate Employee SP

#### Create Prorate Employee:
- Join Date: Mid-month (e.g., 15th of current month)
- Employment Status: Permanent
- Follow same steps as above

**Expected Result**: SP deduction calculated proportionally based on working days

## Troubleshooting

### Issue: SP Deduction rule not appearing in payslip

**Possible Causes:**
1. Salary rule not installed
2. Employee has no active SP
3. SP not approved
4. SP validity expired

**Solutions:**
1. Update module: `python odoo-bin -u hr_disciplinary_action -d your_database`
2. Check SP state and validity
3. Verify payslip period overlaps with SP validity

### Issue: Part-time employee cannot be selected in SP form

**Possible Causes:**
1. Employee contract not set to part-time
2. Contract not in "Running" state

**Solutions:**
1. Check employee contract employment_state
2. Ensure contract is active

### Issue: SP Deduction amount incorrect

**Possible Causes:**
1. Working days calculation error
2. Department schedule_prorate not set
3. Salary rule code error

**Solutions:**
1. Check worked_days_line_ids in payslip
2. Set department.schedule_prorate for prorate employees
3. Review salary rule Python code

## Testing Commands

### Run Unit Tests:
```bash
python odoo-bin -d your_database -i hr_disciplinary_action --test-enable --stop-after-init
```

### Manual SQL Checks:
```sql
-- Check active SPs for employee
SELECT e.name, da.action, da.state, da.additional_salary_deduction, 
       da.salary_deduction_percentage, da.remaining_validity
FROM hr_disciplinary_action da
JOIN hr_employee e ON da.employee_id = e.id
WHERE da.state = 'approved' 
  AND da.additional_salary_deduction = true
  AND da.remaining_validity > 0;

-- Check payslip SP detection
SELECT p.number, e.name, p.has_sp_deduction, p.sp_deduction_percentage
FROM hr_payslip p
JOIN hr_employee e ON p.employee_id = e.id
WHERE p.has_sp_deduction = true;
```

## Success Criteria

✅ Part-time employees can have SP created and approved
✅ Full-time employees can have SP created and approved  
✅ Prorate employees can have SP created and approved
✅ Payslips correctly detect SP for all employee types
✅ SP Deduction salary rule appears in payslips
✅ Deduction amounts calculated correctly based on employment type
✅ Unit tests pass

## Contact
If issues persist, check the module logs and verify all dependencies are installed correctly.
