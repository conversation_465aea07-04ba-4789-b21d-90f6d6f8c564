# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from datetime import date, datetime
from dateutil.relativedelta import relativedelta


class TestSPDeduction(TransactionCase):
    
    def setUp(self):
        super(TestSPDeduction, self).setUp()
        
        # Create test department with schedule_prorate
        self.department = self.env['hr.department'].create({
            'name': 'Test Department',
            'schedule_prorate': 23,  # 23 working days per month
        })
        
        # Create test disciplinary type
        self.disciplinary_type = self.env['hr.disciplinary.type'].create({
            'name': 'Test Violation',
            'description': 'Test disciplinary action type'
        })
        
        # Create test employees with different employment states
        self.full_time_employee = self.env['hr.employee'].create({
            'name': 'Full Time Employee',
            'department_id': self.department.id,
        })
        
        self.part_time_employee = self.env['hr.employee'].create({
            'name': 'Part Time Employee', 
            'department_id': self.department.id,
        })
        
        self.prorate_employee = self.env['hr.employee'].create({
            'name': 'Prorate Employee',
            'department_id': self.department.id,
            'join_date': date.today() - relativedelta(days=15),  # Joined mid-month
        })
        
        # Create contracts
        self.full_time_contract = self.env['hr.contract'].create({
            'name': 'Full Time Contract',
            'employee_id': self.full_time_employee.id,
            'wage': 5000000,
            'employment_state': 'permanent',
            'state': 'open',
            'date_start': date.today() - relativedelta(months=1),
        })
        
        self.part_time_contract = self.env['hr.contract'].create({
            'name': 'Part Time Contract',
            'employee_id': self.part_time_employee.id,
            'wage': 3000000,
            'employment_state': 'part_time',
            'state': 'open',
            'date_start': date.today() - relativedelta(months=1),
        })
        
        self.prorate_contract = self.env['hr.contract'].create({
            'name': 'Prorate Contract',
            'employee_id': self.prorate_employee.id,
            'wage': 4000000,
            'employment_state': 'permanent',
            'state': 'open',
            'date_start': date.today() - relativedelta(days=15),
        })
    
    def test_create_sp_for_part_time_employee(self):
        """Test that SP can be created for part-time employees"""
        sp_action = self.env['hr.disciplinary.action'].create({
            'employee_id': self.part_time_employee.id,
            'type_id': self.disciplinary_type.id,
            'action': 'sp1',
            'additional_salary_deduction': True,
            'salary_deduction_percentage': 10.0,
            'salary_deduction_validity': 2,
        })
        
        # Submit and approve the SP
        sp_action.action_submit()
        sp_action.action_approve()
        
        self.assertEqual(sp_action.state, 'approved')
        self.assertTrue(sp_action.effective_date)
        self.assertEqual(sp_action.remaining_validity, 2)
    
    def test_create_sp_for_full_time_employee(self):
        """Test that SP can be created for full-time employees"""
        sp_action = self.env['hr.disciplinary.action'].create({
            'employee_id': self.full_time_employee.id,
            'type_id': self.disciplinary_type.id,
            'action': 'sp2',
            'additional_salary_deduction': True,
            'salary_deduction_percentage': 15.0,
            'salary_deduction_validity': 3,
        })
        
        # Submit and approve the SP
        sp_action.action_submit()
        sp_action.action_approve()
        
        self.assertEqual(sp_action.state, 'approved')
        self.assertTrue(sp_action.effective_date)
        self.assertEqual(sp_action.remaining_validity, 3)
    
    def test_create_sp_for_prorate_employee(self):
        """Test that SP can be created for prorate employees"""
        sp_action = self.env['hr.disciplinary.action'].create({
            'employee_id': self.prorate_employee.id,
            'type_id': self.disciplinary_type.id,
            'action': 'sp1',
            'additional_salary_deduction': True,
            'salary_deduction_percentage': 5.0,
            'salary_deduction_validity': 1,
        })
        
        # Submit and approve the SP
        sp_action.action_submit()
        sp_action.action_approve()
        
        self.assertEqual(sp_action.state, 'approved')
        self.assertTrue(sp_action.effective_date)
        self.assertEqual(sp_action.remaining_validity, 1)
    
    def test_payslip_detects_sp_for_part_time(self):
        """Test that payslip correctly detects SP for part-time employees"""
        # Create and approve SP for part-time employee
        sp_action = self.env['hr.disciplinary.action'].create({
            'employee_id': self.part_time_employee.id,
            'type_id': self.disciplinary_type.id,
            'action': 'sp1',
            'additional_salary_deduction': True,
            'salary_deduction_percentage': 10.0,
            'salary_deduction_validity': 2,
        })
        sp_action.action_submit()
        sp_action.action_approve()
        
        # Create payslip
        payslip = self.env['hr.payslip'].create({
            'employee_id': self.part_time_employee.id,
            'contract_id': self.part_time_contract.id,
            'date_from': date.today().replace(day=1),
            'date_to': date.today().replace(day=28),
        })
        
        # Check if SP is detected
        self.assertTrue(payslip.has_sp_deduction)
        self.assertEqual(payslip.sp_deduction_percentage, 10.0)
        self.assertEqual(len(payslip.disciplinary_action_ids), 1)
    
    def test_payslip_detects_sp_for_full_time(self):
        """Test that payslip correctly detects SP for full-time employees"""
        # Create and approve SP for full-time employee
        sp_action = self.env['hr.disciplinary.action'].create({
            'employee_id': self.full_time_employee.id,
            'type_id': self.disciplinary_type.id,
            'action': 'sp2',
            'additional_salary_deduction': True,
            'salary_deduction_percentage': 15.0,
            'salary_deduction_validity': 3,
        })
        sp_action.action_submit()
        sp_action.action_approve()
        
        # Create payslip
        payslip = self.env['hr.payslip'].create({
            'employee_id': self.full_time_employee.id,
            'contract_id': self.full_time_contract.id,
            'date_from': date.today().replace(day=1),
            'date_to': date.today().replace(day=28),
        })
        
        # Check if SP is detected
        self.assertTrue(payslip.has_sp_deduction)
        self.assertEqual(payslip.sp_deduction_percentage, 15.0)
        self.assertEqual(len(payslip.disciplinary_action_ids), 1)
    
    def test_payslip_detects_sp_for_prorate(self):
        """Test that payslip correctly detects SP for prorate employees"""
        # Create and approve SP for prorate employee
        sp_action = self.env['hr.disciplinary.action'].create({
            'employee_id': self.prorate_employee.id,
            'type_id': self.disciplinary_type.id,
            'action': 'sp1',
            'additional_salary_deduction': True,
            'salary_deduction_percentage': 5.0,
            'salary_deduction_validity': 1,
        })
        sp_action.action_submit()
        sp_action.action_approve()
        
        # Create payslip
        payslip = self.env['hr.payslip'].create({
            'employee_id': self.prorate_employee.id,
            'contract_id': self.prorate_contract.id,
            'date_from': date.today().replace(day=1),
            'date_to': date.today().replace(day=28),
        })
        
        # Check if SP is detected
        self.assertTrue(payslip.has_sp_deduction)
        self.assertEqual(payslip.sp_deduction_percentage, 5.0)
        self.assertEqual(len(payslip.disciplinary_action_ids), 1)
